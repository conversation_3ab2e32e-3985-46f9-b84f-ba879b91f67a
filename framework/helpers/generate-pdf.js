import puppeteer from 'puppeteer';

export default async function (content = '', options = {}) {
    const browser = await puppeteer.launch({
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--single-process', // <- this one doesn't work in Windows
            '--disable-gpu'
        ],
        handleSIGINT: true,
        handleSIGTERM: true,
        handleSIGHUP: true,
        headless: 'new',
        devtools: false,
        executablePath: '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'
    });
    const page = await browser.newPage();

    await page.emulateMediaType('print');
    await page.setContent(content, {waitUntil: 'networkidle2'});

    const pdf = await page.pdf({
        displayHeaderFooter: true,
        footerTemplate:
            '<div style="text-align: right;width: 297mm;font-size: 8px;"><span style="margin-right: 1cm"><span class="pageNumber"></span> / <span class="totalPages"></span></span></div>',
        ...options
    });

    await page.close();
    await browser.close();

    return pdf;
}
