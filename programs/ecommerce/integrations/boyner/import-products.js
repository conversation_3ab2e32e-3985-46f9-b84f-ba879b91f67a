import _ from 'lodash';
import {apiRequest} from './utils';
import {removeUnmatchedIntegrationProducts} from '../utils';

export default async function (app, store, onProgress) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;
    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };
    const now = app.datetime.local().toJSDate();
    let page = 0;
    let currentCount = 0;
    let totalCount = 0;

    // Delete store product that does not have barcode.
    await app.collection('ecommerce.store-products').remove({
        storeId: store._id,
        $or: [{productBarcode: {$eq: null}}, {productBarcode: {$eq: ''}}, {productBarcode: {$exists: false}}],
        $disableSoftDelete: true
    });

    const matchedProducts = [];
    await (async function iterator() {
        const result = await apiRequest({
            url,
            path: `sapigw/suppliers/${supplierId}/products?page=${page}&size=50`,
            supplierId,
            apiKey,
            apiSecret
        });
        totalCount = result.totalElements;

        if (Array.isArray(result.content) && result.content.length > 0) {
            const items = [];

            const barcodes = [];
            const integrationProductsMap = {};

            for (const item of result.content) {
                const barcode = (item.barcode ?? '').toString().trim();
                if (!barcode) continue;

                barcodes.push(barcode);
                integrationProductsMap[barcode] = item;
            }

            if (barcodes.length > 0) {
                const erpProducts = await app.collection('inventory.products').find({
                    $or: [
                        {barcode: {$in: barcodes}},
                        {'barcodes.barcode': {$in: barcodes}}
                    ]
                });

                const erpProductsMap = {};
                for (const erpProduct of erpProducts) {
                    if (barcodes.includes(erpProduct.barcode)) {
                        erpProductsMap[erpProduct.barcode] = erpProduct;
                    }

                    if (Array.isArray(erpProduct.barcodes)) {
                        for (const barcodeInfo of erpProduct.barcodes) {
                            if (barcodes.includes(barcodeInfo.barcode)) {
                                erpProductsMap[barcodeInfo.barcode] = erpProduct;
                            }
                        }
                    }
                }

                // Match products
                for (const barcode of barcodes) {
                    const erpProduct = erpProductsMap[barcode];
                    const integrationProduct = integrationProductsMap[barcode];

                    if (!!erpProduct) {
                        items.push({
                            erpProduct,
                            integrationProduct
                        });
                    } else {
                        log({
                            level: 'error',
                            message: app.translate(
                                'The {{productName}} integration product could not be matched. No ERP product found for the relevant product! Barcode: {{barcode}}',
                                {
                                    productName: integrationProduct.title,
                                    barcode
                                }
                            )
                        });
                    }
                }
            }

            if (items.length > 0) {
                const storeProductOperations = [];

                // Get product ids.
                const productIds = items.map(item => item.erpProduct._id);

                // Get brands.
                const brands = await app.collection('inventory.product-brands').find({
                    _id: {$in: items.map(item => item.erpProduct.brandId).filter(brandId => !!brandId)},
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });
                const brandsMap = {};
                for (const brand of brands) {
                    brandsMap[brand._id] = brand;
                }

                // Get product prices.
                const productPricesMap = {};
                if (store.pricingPolicy === 'use-price-list' && !!store.priceListId) {
                    const productPrices = (
                        await app.collection('sale.product-prices').find({
                            productId: {$in: productIds},
                            priceListId: store.priceListId,
                            currencyId: store.currencyId,
                            validFrom: {
                                $lte: now
                            },
                            validTo: {
                                $gte: now
                            }
                        })
                    ).filter(p => p.min <= 1 || _.isUndefined(p.min));

                    for (const productPrice of productPrices) {
                        productPricesMap[`${store.priceListId}${productPrice.productId}`] = productPrice.price;
                    }
                }

                // Get discounted product prices.
                const discountedProductPricesMap = {};
                if (store.pricingPolicy === 'use-price-list' && !!store.discountedPriceListId) {
                    const productPrices = (
                        await app.collection('sale.product-prices').find({
                            productId: {$in: productIds},
                            priceListId: store.discountedPriceListId,
                            currencyId: store.currencyId,
                            validFrom: {
                                $lte: now
                            },
                            validTo: {
                                $gte: now
                            }
                        })
                    ).filter(p => p.min <= 1 || _.isUndefined(p.min));

                    for (const productPrice of productPrices) {
                        discountedProductPricesMap[`${store.discountedPriceListId}${productPrice.productId}`] =
                            productPrice.price;
                    }
                }

                // Get stock report.
                const productStocksMap = {};
                if (store.stockPolicy !== 'manuel') {
                    const stockResult = await app.rpc('inventory.get-stock-report', {
                        productId: productIds,
                        warehouseId: store.warehouseId
                    });

                    if (!!stockResult && stockResult.length > 0) {
                        for (const sr of stockResult) {
                            productStocksMap[sr.productId] = sr;
                        }
                    }
                }

                for (const item of items) {
                    const product = item.erpProduct;

                    const barcode = (item.integrationProduct.barcode ?? '').toString().trim();
                    const matchedBarcodeInfo = product.barcodes.find(pb => pb.barcode === barcode) ?? {};

                    const existingStoreProduct = await app.collection('ecommerce.store-products').findOne({
                        storeId: store._id,
                        productBarcode: barcode
                    });

                    if (!!existingStoreProduct) {
                        const sp = {};

                        sp.integrationId = item.integrationProduct.id.toString();
                        sp.unitId = matchedBarcodeInfo.unitId || product.salesUnitId;
                        sp.isPublished = true;
                        sp.isSynchronized = true;

                        sp.integrationStatus = 'waiting';
                        if (item.integrationProduct.approved === true) {
                            sp.integrationStatus = 'approved';
                        }
                        if (item.integrationProduct.rejected === true) {
                            sp.integrationStatus = 'rejected';

                            sp.integrationRejectionReason = [];
                            for (const reason of item.integrationProduct.rejectReasonDetails ?? []) {
                                sp.integrationRejectionReason.push(
                                    `${reason.rejectReason ?? ''}\n${reason.rejectReasonDetail ?? ''}`
                                );
                            }
                            sp.integrationRejectionReason = sp.integrationRejectionReason.join('\n\n');
                        } else {
                            sp.integrationRejectionReason = '';
                        }
                        if (item.integrationProduct.blacklisted === true) {
                            sp.integrationStatus = 'blacklisted';
                        }

                        if (sp.integrationId) {
                            matchedProducts.push({
                                productId: product._id,
                                productName: product.definition,
                                barcode: matchedBarcodeInfo.barcode || product.barcode
                            });
                        }

                        storeProductOperations.push({
                            updateOne: {
                                filter: {_id: existingStoreProduct._id},
                                update: {$set: sp}
                            }
                        });
                    } else {
                        const sp = {};
                        sp.storeId = store._id;
                        sp.integrationId = item.integrationProduct.id.toString();
                        sp.productId = product._id;
                        sp.productImage = product.image;
                        sp.productCode = product.code;
                        sp.productDefinition = product.definition;
                        sp.productBarcode = matchedBarcodeInfo.barcode || product.barcode;
                        sp.productCategoryId = product.categoryId;
                        sp.productCategoryPath = product.categoryPath;
                        sp.productGroupIds = product.groupIds;
                        sp.unitId = matchedBarcodeInfo.unitId || product.salesUnitId;
                        sp.isPublished = true;
                        sp.isSynchronized = true;
                        sp.currencyId = store.currencyId;
                        sp.taxApplication = store.taxApplication;
                        sp.taxId = product.salesTaxId;
                        sp.salesPrice = product.salesPrice;
                        sp.quantity = 0;
                        sp.createdAt = app.datetime.local().toJSDate();
                        sp.updatedAt = sp.createdAt;

                        sp.barcode = sp.productBarcode;
                        sp.title = sp.productDefinition;
                        sp.productMainId = product.code;
                        sp.brandId = product.brandId;
                        sp.categoryId = product.categoryId;
                        sp.quantity = sp.quantity || 0;
                        sp.stockCode = product.code;
                        sp.dimensionalWeight = product.dimensionalWeight || 1;
                        sp.description = product.description || product.definition;
                        sp.currencyType = 'TRY';
                        sp.listPrice = typeof sp.salesPrice === 'number' ? sp.salesPrice : 0;
                        sp.salePrice = typeof sp.salesPrice === 'number' ? sp.salesPrice : 0;
                        sp.vatRate = product.vatRate || 18;
                        sp.cargoCompanyId = store.cargoCompanyId || 10;
                        sp.deliveryDuration = store.deliveryDuration || 3;
                        sp.images = (Array.isArray(product.images) && product.images.length > 0)
                            ? product.images.slice(0, 8).map(url => ({ url }))
                            : (product.image ? [{ url: product.image }] : []);
                        sp.attributes = Array.isArray(product.attributes)
                            ? product.attributes.map(attr => ({
                                attributeId: attr.attributeId,
                                attributeValueId: attr.attributeValueId,
                                customAttributeValue: attr.customAttributeValue || ''
                            })) : [];
                        if (store.shipmentAddressId) sp.shipmentAddressId = store.shipmentAddressId;
                        if (store.returningAddressId) sp.returningAddressId = store.returningAddressId;

                        sp.integrationStatus = 'waiting';
                        if (item.integrationProduct.approved === true) {
                            sp.integrationStatus = 'approved';
                        }
                        if (item.integrationProduct.rejected === true) {
                            sp.integrationStatus = 'rejected';
                            sp.integrationRejectionReason = [];
                            for (const reason of item.integrationProduct.rejectReasonDetails ?? []) {
                                sp.integrationRejectionReason.push(
                                    `${reason.rejectReason ?? ''}\n${reason.rejectReasonDetail ?? ''}`
                                );
                            }
                            sp.integrationRejectionReason = sp.integrationRejectionReason.join('\n\n');
                        } else {
                            sp.integrationRejectionReason = '';
                        }
                        if (item.integrationProduct.blacklisted === true) {
                            sp.integrationStatus = 'blacklisted';
                        }

                        if (!!product.brandId) {
                            const brand = brandsMap[product.brandId];
                            sp.productBrandId = product.brandId;
                            sp.productBrandName = brand.name;
                            sp.productBrandSlug = brand.slug;
                        }

                         if (sp.integrationId) {
                            matchedProducts.push({
                                productId: product._id,
                                productName: product.definition,
                                barcode: matchedBarcodeInfo.barcode || product.barcode
                            });
                        }

                        if (store.pricingPolicy === 'use-price-list' && !!store.priceListId) {
                            const price = productPricesMap[`${store.priceListId}${product._id}`];
                            if (typeof price === 'number') {
                                sp.salesPrice = price;
                                sp.listPrice = price;
                                sp.salePrice = price;
                            }
                        }
                        if (store.pricingPolicy === 'use-price-list' && !!store.discountedPriceListId) {
                            const price = discountedProductPricesMap[`${store.discountedPriceListId}${product._id}`];
                            if (typeof price === 'number') {
                                sp.discountedSalesPrice = price;
                                if (sp.salesPrice > 0) {
                                    sp.discount = app.round(
                                        ((sp.salesPrice - sp.discountedSalesPrice) / sp.salesPrice) * 100,
                                        'percentage'
                                    );
                                } else {
                                    delete sp.discountedSalesPrice;
                                }
                            }
                        }

                        if (store.taxApplication === 'tax-included') {
                            if (sp.salesPrice > 0 && !!sp.taxId) {
                                const taxResult = await app.rpc('kernel.common.calculate-tax', {
                                    taxId: sp.taxId,
                                    amount: sp.salesPrice
                                });
                                const taxTotal = taxResult.taxTotal;
                                const price = app.round(sp.salesPrice + taxTotal, 'unit-price');
                                if (typeof sp.discountedSalesPrice === 'number') {
                                    const discountedPriceTaxTotal = app.round(
                                        (taxTotal * sp.discountedSalesPrice) / sp.salesPrice,
                                        'unit-price'
                                    );
                                    sp.discountedSalesPrice = app.round(
                                        sp.discountedSalesPrice + discountedPriceTaxTotal,
                                        'unit-price'
                                    );
                                }
                                sp.salesPrice = price;
                                sp.listPrice = price;
                                sp.salePrice = price;
                            }
                        }

                        if (store.stockPolicy !== 'manuel') {
                            const r = productStocksMap[product._id];
                            const unitRatios = product.unitRatios;
                            let qty = 0;
                            if (!!r) {
                                if (store.stockPolicy === 'use-stock-on-hand') {
                                    qty = r.stockQuantity || 0;
                                } else if (store.stockPolicy === 'use-available-stock') {
                                    qty = r.availableQuantity || 0;
                                } else if (store.stockPolicy === 'use-available-stock-without-ordered-reservations') {
                                    qty = (r.availableQuantity || 0) - (r.orderedQuantity || 0);
                                } else {
                                    qty = 0;
                                }
                            }
                            if (!!unitRatios && _.isNumber(unitRatios[sp.unitId]) && unitRatios[sp.unitId] !== 0) {
                                const ratio = unitRatios[sp.unitId] || 1;
                                qty = Math.floor(qty / ratio);
                            }
                            sp.quantity = qty;
                        }

                        if (!!store.useProductImportStocks) {
                            let totalVendorStocks = 0;
                            const pcItems = await app.collection('purchase.procurement-catalog').find({
                                productId: product._id,
                                $select: ['qty']
                            });
                            for (const pcItem of pcItems) {
                                totalVendorStocks += pcItem.qty || 0;
                            }
                            sp.quantity += totalVendorStocks;
                        }

                        if (!sp.integrationId) {
                            log({
                                level: 'error',
                                message: app.translate(
                                    'The {{productName}} integration product could not be matched. Integration identifier was not found! Barcode: {{barcode}}',
                                    {productName: sp.productDefinition, barcode: sp.productBarcode}
                                )
                            });
                        } else {
                            storeProductOperations.push({
                                insertOne: {
                                    document: sp
                                }
                            });
                        }
                    }
                }

                if (storeProductOperations.length > 0) {
                    await app.collection('ecommerce.store-products').bulkWrite(storeProductOperations);
                }
            }

            page++;
            currentCount += result.content.length;

            if (!!onProgress && totalCount > 0) {
                onProgress((currentCount / totalCount) * 100);
            }

            await iterator();
        } else if (!!onProgress) {
            onProgress(100);
        }
    })();

    await removeUnmatchedIntegrationProducts({app, store, products: matchedProducts});
}
