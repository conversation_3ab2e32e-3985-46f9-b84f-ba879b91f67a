import _ from 'lodash';
import { apiRequest } from './utils';

export default async function (app, store, orderId, carrierId) {
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error('An error occurred while parsing the integration parameters:', error);
        return;
    }

    const { url, supplierId, apiKey, apiSecret } = integrationParams;


    const log = payload => {
        payload.collection = 'ecommerce.stores';
        payload.documentId = store._id;
        payload.documentIdentifier = store.code;
        payload.method = 'update';
        payload.collectionName = `${app.translate('E-Commerce')} / ${app.translate('Stores')}`;

        return app.log(payload);
    };

    const deliveryOption = store.deliveryOptions.find(sdo => sdo.carrierId === carrierId);
    if (!deliveryOption || !deliveryOption.integrationId) {
        log({
            level: 'error',
            message: app.translate('No delivery option found for the provided carrier!')
        });
        return;
    }

    const order = await app.collection('sale.orders').findOne({
        _id: orderId,
        $select: ['_id', 'code', 'integrationPayload', 'carrierId']
    });

    if (!order) {
        log({ level: 'error', message: `The order with ID ${orderId} could not be found in the database.` });
        return;
    }
    
    const providerCode = deliveryOption.integrationId.split('-')[1];

    try {
        await apiRequest({
            url,
            method: 'POST',
            path: 'sapigw/companies/update-company-cargos',
            supplierId,
            apiKey,
            apiSecret,
            data: {
                companyId: Number(supplierId),
                workingModel: 1,
                changeChannel: 3,
                cargoId: Number(providerCode),
                reason: 'Change of shipping company',
                description: 'The shipping company was changed via API.'
            }
        });
    } catch (err) {
       console.log(err);
    }


    const orderNumber = order.code.replace(`${store.code}/`, '').trim();
    let integrationOrder = null;

    try {
        const result = await apiRequest({
            url,
            path: `sapigw/suppliers/${supplierId}/orders?orderNumber=${orderNumber}`,
            supplierId,
            apiKey,
            apiSecret
        });

        if (Array.isArray(result.content) && result.content.length > 0) {
            integrationOrder = result.content[0];
        } else if (_.isPlainObject(result.content) && !_.isEmpty(result.content)) {
            integrationOrder = result.content;
        } else {
            console.log('Order not found');
        }
    } catch(err) {
        console.log(err)
        return null;
    }


    if (order.carrierId !== carrierId) {
        await app.collection('sale.orders').bulkWrite([
            {
                updateOne: {
                    filter: {_id: orderId},
                    update: {$set: {carrierId: carrierId}}
                }
            }
        ]);
    }

    if (
        !!integrationOrder &&
        !_.isUndefined(integrationOrder.cargoTrackingNumber) &&
        integrationOrder.cargoTrackingNumber !== null &&
        integrationOrder.cargoTrackingNumber !== ''
    ) {
        return integrationOrder.cargoTrackingNumber.toString();
    } else {
        console.log('Tracking number not found');
    }

    return null;
}