import _ from 'lodash';
import path from 'path';
import {PDFDocument} from 'pdf-lib';
import Random from 'framework/random';
import {closeBrowser, initializeBrowser} from 'framework/helpers/generate-pdf-bulk';

export default {
    name: 'bulk-print-shipping-orders',
    async action({query}, params) {
        const app = this.app;
        const limit = 100;
        let total = null;
        let page = 0;
        let rowCount = 0;
        const progressId = `logistics.shipping-orders.bulk-print-${params.user._id}`;

        app.progress({
            id: progressId,
            status: 'started'
        });

        const operations = [];

        try {
           

            const mergedPdf = await PDFDocument.create();

            do {
                let result = null;

                if (total === null) {
                    result = await app.collection('logistics.shipping-orders').find(
                        {
                            ...query,
                            $skip: page * limit,
                            $limit: limit
                        },
                        {
                            paginate: {
                                default: limit
                            }
                        }
                    );

                    total = result.total;
                } else {
                    result = {
                        data: await app.collection('logistics.shipping-orders').find({
                            ...query,
                            $skip: page * limit,
                            $limit: limit
                        }),
                        total
                    };
                }

                const carriers = await app.collection('logistics.carriers').find({
                    _id: {$in: _.uniq(result.data.map(item => item.carrierId))},
                    $select: ['_id', 'defaultPrintingTemplateId', 'integrationType', 'printingMode'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                const carrierMap = _.keyBy(carriers, '_id');

                const chunks = _.chunk(result.data, 10);

                for (const chunk of chunks) {
                    await Promise.all(
                        chunk.map(async order => {
                            const carrier = carrierMap[order.carrierId];
                            const integrationType = carrier?.integrationType;
                            const printingMode = carrier?.printingMode || 'system-template';
                            let data;

                            if (integrationType === 'hepsijet' && printingMode === 'hepsijet') {
                               try {
                                 data = await app.rpc('logistics.shipping-orders-get-barcode', {
                                    id: order._id
                                });
                                const base64Data = data.split(',')[1];
                                const buffer = Buffer.from(base64Data, 'base64');
                                data = {arrayBuffer: () => Promise.resolve(buffer.buffer)};
                               }
                               catch (error) {
                                console.error('Error getting barcode:', error);
                                rowCount++;
                                return new Promise(resolve => resolve());
                            }
                            } else if (integrationType === 'hepsijet' && printingMode === 'hepsijet-system-template') {
                                try {
                                    const mergedLabelBase64 = await app.rpc(
                                        'logistics.shipping-orders-get-merged-label',
                                        {
                                            id: order._id
                                        }
                                    );

                                    const base64Data = mergedLabelBase64.split(',')[1];
                                    const buffer = Buffer.from(base64Data, 'base64');
                                    data = {arrayBuffer: () => Promise.resolve(buffer.buffer)};
                                } catch (error) {
                                    console.error('Error getting merged label:', error);
                                    rowCount++;
                                    return new Promise(resolve => resolve());
                                }
                            } else {
                                const templateId = carrier?.defaultPrintingTemplateId ?? '';
                                data = await fetch(
                                    app.absoluteUrl(
                                        `/portal/common/output?templateName=logistics.shipping-order-label&templateId=${templateId}&recordId=${order._id}&bulk=true`
                                    )
                                );
                            }

                            const pdf = await PDFDocument.load(await data.arrayBuffer());
                            const pages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());
                            pages.forEach(page => mergedPdf.addPage(page));

                            operations.push({
                                updateOne: {
                                    filter: {_id: order._id},
                                    update: {
                                        $set: {
                                            printingStatus: 'printed',
                                            printedAt: app.datetime.local().toJSDate()
                                        }
                                    }
                                }
                            });

                            rowCount++;

                            // Update progress
                            if (total !== 0) {
                                const percentage = (rowCount / total) * 100;
                                app.progress({
                                    id: progressId,
                                    status: 'info',
                                    percentage
                                });
                            }
                        })
                    );
                }

                page++;
            } while (total > 0 && rowCount < total);

            if (operations.length > 0) {
                await app.collection('logistics.shipping-orders').bulkWrite(operations);

                const filePath = path.join(app.config('paths.temp'), `${Random.id(12)}.pdf`);

                const fileInfo = await app.files.write(filePath, await mergedPdf.save(), {
                    isTemporary: true,
                    name: app.translate('Shipping Orders')
                });

                app.progress({
                    id: progressId,
                    status: 'success',
                    percentage: 100
                });

                return app.absoluteUrl(`files/${fileInfo._id}`);
            }

            app.progress({
                id: progressId,
                status: 'success',
                percentage: 100
            });

            return null;
        } catch (error) {
            app.progress({
                id: progressId,
                status: 'error',
                message: error.message
            });
        } finally {
            await closeBrowser();
        }
    }
};
